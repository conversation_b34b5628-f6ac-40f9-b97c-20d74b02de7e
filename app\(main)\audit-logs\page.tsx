'use client';

import { useState, useMemo, useCallback } from 'react';
import { useStore } from '@/context/store';
import { AuditLog } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, RefreshCw, FileDown, Activity, User, Calendar } from 'lucide-react';

export default function AuditLogsPage() {
  // ✅ استخدام Store API - البيانات متوفرة فوراً من الكاش
  const {
    activities: auditLogs,
    users,
    fetchUsersData,
    isLoading
  } = useStore();

  // حالات التصفية والبحث
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [userFilter, setUserFilter] = useState('all');
  const [operationFilter, setOperationFilter] = useState('all');

  // ✅ دالة تحديث البيانات محسّنة
  const refreshData = useCallback(async () => {
    try {
      await Promise.all([
        fetchAuditLogsData({ forceRefresh: true }),
        fetchUsersData({ forceRefresh: true })
      ]);
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    }
  }, [fetchAuditLogsData, fetchUsersData]);

  // ✅ إحصائيات ذكية مع useMemo
  const auditStats = useMemo(() => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const todayLogs = auditLogs.filter(log => 
      new Date(log.timestamp).toDateString() === today.toDateString()
    );
    
    const yesterdayLogs = auditLogs.filter(log => 
      new Date(log.timestamp).toDateString() === yesterday.toDateString()
    );

    const operationTypes = [...new Set(auditLogs.map(log => log.operation))];
    const uniqueUsers = [...new Set(auditLogs.map(log => log.userId))];

    return {
      total: auditLogs.length,
      today: todayLogs.length,
      yesterday: yesterdayLogs.length,
      operations: operationTypes.length,
      users: uniqueUsers.length
    };
  }, [auditLogs]);

  // ✅ تصفية البيانات الذكية
  const filteredLogs = useMemo(() => {
    let result = auditLogs;

    // تطبيق البحث
    if (searchQuery) {
      result = result.filter(log => 
        log.operation.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.details.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // تطبيق فلتر التاريخ
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          result = result.filter(log => 
            new Date(log.timestamp).toDateString() === now.toDateString()
          );
          break;
        case 'yesterday':
          filterDate.setDate(filterDate.getDate() - 1);
          result = result.filter(log => 
            new Date(log.timestamp).toDateString() === filterDate.toDateString()
          );
          break;
        case 'week':
          filterDate.setDate(filterDate.getDate() - 7);
          result = result.filter(log => new Date(log.timestamp) >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(filterDate.getMonth() - 1);
          result = result.filter(log => new Date(log.timestamp) >= filterDate);
          break;
      }
    }

    // تطبيق فلتر المستخدم
    if (userFilter !== 'all') {
      result = result.filter(log => log.userId === parseInt(userFilter));
    }

    // تطبيق فلتر العملية
    if (operationFilter !== 'all') {
      result = result.filter(log => log.operation === operationFilter);
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    return result.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [auditLogs, searchQuery, dateFilter, userFilter, operationFilter]);

  // ✅ قائمة العمليات الفريدة
  const uniqueOperations = useMemo(() => {
    return [...new Set(auditLogs.map(log => log.operation))];
  }, [auditLogs]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">سجلات التدقيق</h1>
        <Button 
          onClick={refreshData} 
          variant="outline" 
          size="sm"
          disabled={isLoading}
        >
          <RefreshCw className={`ml-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'جاري التحديث...' : 'تحديث البيانات'}
        </Button>
      </div>

      {/* ✅ إحصائيات ذكية */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي السجلات</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auditStats.total}</div>
            <p className="text-xs text-muted-foreground">سجل تدقيق</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">اليوم</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{auditStats.today}</div>
            <p className="text-xs text-muted-foreground">عملية اليوم</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أمس</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{auditStats.yesterday}</div>
            <p className="text-xs text-muted-foreground">عملية أمس</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أنواع العمليات</CardTitle>
            <Badge variant="outline">{auditStats.operations}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{auditStats.operations}</div>
            <p className="text-xs text-muted-foreground">نوع عملية</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المستخدمون النشطون</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{auditStats.users}</div>
            <p className="text-xs text-muted-foreground">مستخدم نشط</p>
          </CardContent>
        </Card>
      </div>

      {/* ✅ فلاتر متقدمة */}
      <Card>
        <CardHeader>
          <CardTitle>فلاتر البحث</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* البحث النصي */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="البحث في السجلات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* فلتر التاريخ */}
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فترة زمنية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفترات</SelectItem>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="yesterday">أمس</SelectItem>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
              </SelectContent>
            </Select>

            {/* فلتر المستخدم */}
            <Select value={userFilter} onValueChange={setUserFilter}>
              <SelectTrigger>
                <SelectValue placeholder="المستخدم" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المستخدمين</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* فلتر العملية */}
            <Select value={operationFilter} onValueChange={setOperationFilter}>
              <SelectTrigger>
                <SelectValue placeholder="نوع العملية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع العمليات</SelectItem>
                {uniqueOperations.map((operation) => (
                  <SelectItem key={operation} value={operation}>
                    {operation}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* زر إعادة تعيين الفلاتر */}
          <div className="flex justify-end mt-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setDateFilter('all');
                setUserFilter('all');
                setOperationFilter('all');
              }}
            >
              إعادة تعيين الفلاتر
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* ✅ جدول السجلات المحسّن */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>سجلات التدقيق ({filteredLogs.length})</CardTitle>
            <Button variant="outline" size="sm">
              <FileDown className="ml-2 h-4 w-4" />
              تصدير CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الطابع الزمني</TableHead>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>العملية</TableHead>
                  <TableHead>التفاصيل</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <RefreshCw className="animate-spin h-6 w-6 mr-2" />
                        جاري تحميل السجلات...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                      لا توجد سجلات تدقيق تطابق المعايير المحددة
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {new Date(log.timestamp).toLocaleDateString('ar-SA')}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {new Date(log.timestamp).toLocaleTimeString('ar-SA')}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{log.userId}</Badge>
                          <span>{log.username}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={log.operation.includes('DELETE') ? 'destructive' : 
                                  log.operation.includes('CREATE') ? 'default' : 'secondary'}
                        >
                          {log.operation}
                        </Badge>
                      </TableCell>
                      <TableCell className="max-w-md">
                        <div className="truncate" title={log.details}>
                          {log.details}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
